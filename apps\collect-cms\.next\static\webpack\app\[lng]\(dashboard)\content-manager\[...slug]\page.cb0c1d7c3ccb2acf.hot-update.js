"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/contexts/BuilderContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/BuilderContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageBuilderContext: function() { return /* binding */ PageBuilderContext; },\n/* harmony export */   PageBuilderProvider: function() { return /* binding */ PageBuilderProvider; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ObjectUtils!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/utils/object-util.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mock_Builder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/mock/Builder */ \"(app-pages-browser)/./src/mock/Builder.ts\");\n/* __next_internal_client_entry_do_not_use__ PageBuilderContext,PageBuilderProvider auto */ \n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\nvar PageBuilderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext({});\nvar PageBuilderProvider = function(props) {\n    _s();\n    var _props_value;\n    var propsValue = (_props_value = props.value) !== null && _props_value !== void 0 ? _props_value : {};\n    var _propsValue_globals;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_globals = propsValue.globals) !== null && _propsValue_globals !== void 0 ? _propsValue_globals : {}), 1), globals = _useState[0];\n    var _propsValue_data;\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_data = propsValue.data) !== null && _propsValue_data !== void 0 ? _propsValue_data : {}), 2), data = _useState1[0], setData = _useState1[1];\n    var _propsValue_contentType;\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_contentType = propsValue.contentType) !== null && _propsValue_contentType !== void 0 ? _propsValue_contentType : {}), 1), contentType = _useState2[0];\n    var _propsValue_components;\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_components = propsValue.components) !== null && _propsValue_components !== void 0 ? _propsValue_components : {}), 1), components = _useState3[0];\n    var _propsValue_locale;\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_locale = propsValue.locale) !== null && _propsValue_locale !== void 0 ? _propsValue_locale : \"en\"), 1), locale = _useState4[0];\n    var _useState5 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propsValue.configuration), 1), configuration = _useState5[0];\n    var _useState6 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}), 2), historyChanges = _useState6[0], setHistoryChanges = _useState6[1];\n    var _useState7 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isPreview = _useState7[0], setIsPreview = _useState7[1];\n    var _propsValue_slug;\n    var _useState8 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_slug = propsValue.slug) !== null && _propsValue_slug !== void 0 ? _propsValue_slug : []), 1), slug = _useState8[0];\n    var _useState9 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        left: true,\n        right: true\n    }), 2), expandedSidebar = _useState9[0], setExpandedSidebar = _useState9[1];\n    var _propsValue_screenTypes;\n    var _useState10 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_screenTypes = propsValue.screenTypes) !== null && _propsValue_screenTypes !== void 0 ? _propsValue_screenTypes : _mock_Builder__WEBPACK_IMPORTED_MODULE_3__.defaultConfig.screenTypes), 1), screenTypes = _useState10[0];\n    var _useState11 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Object.values(screenTypes)[0]), 2), screenSize = _useState11[0], setScreenSize = _useState11[1];\n    var _useState12 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now()), 2), updatedAt = _useState12[0], setUpdatedAt = _useState12[1];\n    var _useState13 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key: \"\",\n        id: -1\n    }), 2), editingIden = _useState13[0], setEditingIden = _useState13[1];\n    var _useState14 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), childComponentData = _useState14[0], setChildComponentData = _useState14[1];\n    var _useState15 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), layerPos = _useState15[0], setLayerPos = _useState15[1];\n    var _useState16 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        url: \"\"\n    }), 2), mediaInfoData = _useState16[0], setMediaInfoData = _useState16[1];\n    var _useState17 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), activeMediaId = _useState17[0], setActiveMediaId = _useState17[1];\n    // Normalize data input\n    var normalizedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        var commonData = data.data;\n        var orgData = commonData !== null && commonData !== void 0 ? commonData : {};\n        var _ObjectUtils_elevateProperty = _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__.ObjectUtils.elevateProperty(orgData, \"data\"), components = _ObjectUtils_elevateProperty.components, _$props = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__._)(_ObjectUtils_elevateProperty, [\n            \"components\"\n        ]);\n        var isTempKeyExisted = components.some(function(component) {\n            return component.__temp_key__;\n        });\n        return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, _$props), {\n            components: isTempKeyExisted ? components : components.map(function(component, index) {\n                return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, component), {\n                    __temp_key__: index + 1\n                });\n            })\n        });\n    }, [\n        data\n    ]);\n    var value = {\n        globals: globals,\n        components: components,\n        data: data,\n        setData: setData,\n        contentType: contentType,\n        locale: locale,\n        configuration: configuration,\n        historyChanges: historyChanges,\n        setHistoryChanges: setHistoryChanges,\n        isPreview: isPreview,\n        setIsPreview: setIsPreview,\n        slug: slug,\n        expandedSidebar: expandedSidebar,\n        setExpandedSidebar: setExpandedSidebar,\n        screenTypes: screenTypes,\n        screenSize: screenSize,\n        setScreenSize: setScreenSize,\n        updatedAt: updatedAt,\n        setUpdatedAt: setUpdatedAt,\n        editingIden: editingIden,\n        setEditingIden: setEditingIden,\n        normalizedData: normalizedData,\n        childComponentData: childComponentData,\n        setChildComponentData: setChildComponentData,\n        layerPos: layerPos,\n        setLayerPos: setLayerPos,\n        mediaInfoData: mediaInfoData,\n        setMediaInfoData: setMediaInfoData,\n        activeMediaId: activeMediaId,\n        setActiveMediaId: setActiveMediaId\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageBuilderContext.Provider, {\n        value: value,\n        children: props.children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\contexts\\\\BuilderContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 9\n    }, _this);\n};\n_s(PageBuilderProvider, \"+BDk1do5INBM0Js+WXjEYvV/TGY=\");\n_c = PageBuilderProvider;\nvar _c;\n$RefreshReg$(_c, \"PageBuilderProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/BuilderContext.tsx\n"));

/***/ })

});